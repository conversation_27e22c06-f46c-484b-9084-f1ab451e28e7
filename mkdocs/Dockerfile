FROM python:3.13.7

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY docs/requirements.txt ./requirements.txt

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire project
COPY . .

# Expose port 8000 (mkdocs default)
EXPOSE 8000

# Command to serve mkdocs
CMD ["mkdocs", "serve", "--dev-addr=0.0.0.0:8000"]
