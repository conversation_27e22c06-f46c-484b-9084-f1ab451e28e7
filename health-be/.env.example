# Health Monitor Backend - Environment Variables

# Server Configuration
PORT=5001
NODE_ENV=development

# Database Configuration (MariaDB)
DB_HOST=mariadb
DB_PORT=3306
DB_DATABASE=health_monitor
DB_USER=health_user
DB_PASSWORD=your_secure_password
DB_ROOT_PASSWORD=your_root_password

# Docker Configuration
# El socket de Docker se monta como volumen en docker-compose.yml
DOCKER_SOCKET_PATH=/var/run/docker.sock

# CORS Configuration (opcional, ya está configurado en el código)
# CORS_ORIGINS=https://status.msarknet.me,https://msarknet.me
