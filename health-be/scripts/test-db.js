#!/usr/bin/env node

/**
 * Script de prueba para verificar la conexión a la base de datos
 * y las operaciones básicas del modelo MonitoredService
 */

const { testConnection } = require('../src/config/database');
const MonitoredService = require('../src/models/MonitoredService');

async function testDatabase() {
  console.log('🧪 Testing database connection and operations...\n');

  try {
    // 1. Probar conexión
    console.log('1️⃣ Testing database connection...');
    const connected = await testConnection();
    if (!connected) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection successful\n');

    // 2. Contar servicios
    console.log('2️⃣ Counting services...');
    const count = await MonitoredService.count();
    console.log(`✅ Found ${count} active services\n`);

    // 3. Listar todos los servicios
    console.log('3️⃣ Listing all services...');
    const services = await MonitoredService.getAll();
    console.log(`✅ Retrieved ${services.length} services:`);
    services.forEach(service => {
      console.log(`   - ${service.name} (${service.container_name})`);
    });
    console.log('');

    // 4. Buscar un servicio específico
    console.log('4️⃣ Testing service lookup...');
    const traefik = await MonitoredService.getByName('traefik');
    if (traefik) {
      console.log(`✅ Found service: ${traefik.name} -> ${traefik.url || 'No URL'}`);
    } else {
      console.log('⚠️  Traefik service not found');
    }
    console.log('');

    // 5. Buscar por nombre de contenedor
    console.log('5️⃣ Testing container name lookup...');
    const mariadb = await MonitoredService.getByContainerName('mariadb');
    if (mariadb) {
      console.log(`✅ Found service by container: ${mariadb.name}`);
    } else {
      console.log('⚠️  MariaDB service not found');
    }

    console.log('\n🎉 All database tests passed!');

  } catch (error) {
    console.error('❌ Database test failed:', error.message);
    process.exit(1);
  }
}

// Ejecutar pruebas
if (require.main === module) {
  testDatabase();
}

module.exports = { testDatabase };
