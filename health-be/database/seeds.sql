-- Inserts para los servicios monitoreados
-- Basado en la configuración actual de MONITORED_SERVICES

-- Limpiar datos existentes (opcional, comentar si no quieres borrar datos)
-- DELETE FROM monitored_services;

-- Insertar los servicios actuales
INSERT INTO monitored_services (name, container_name, url, icon, description, is_active) VALUES
('Traefik', 'traefik', 'https://traefik.msarknet.me', '⚙️', 'Panel de control del proxy reverso', TRUE),
('Adminer', 'adminer', 'https://adminer.msarknet.me', '🗄️', 'Administrador de base de datos', TRUE),
('MariaDB', 'mariadb', NULL, '🗃️', 'Base de datos MariaDB', TRUE),
('Cerebro Frontend', 'cerebro-fe', 'https://msarknet.me', '🧠', 'Frontend React de Cerebro', TRUE),
('<PERSON><PERSON>bro Backend', 'cerebro-be', 'https://api.msarknet.me', '⚡', 'Backend Node.js de Cerebro', TRUE);

-- Verificar los datos insertados
SELECT 
    id,
    name,
    container_name,
    url,
    icon,
    description,
    is_active,
    created_at
FROM monitored_services 
ORDER BY id;
