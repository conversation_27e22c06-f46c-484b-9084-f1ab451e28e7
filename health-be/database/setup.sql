-- Script completo de configuración de la base de datos
-- Ejecutar este archivo para crear el schema completo y insertar los datos iniciales

-- ============================================================================
-- SCHEMA CREATION
-- ============================================================================

-- Tabla para almacenar los servicios monitoreados
CREATE TABLE IF NOT EXISTS monitored_services (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT 'Nombre del servicio',
    container_name VARCHAR(100) NOT NULL UNIQUE COMMENT 'Nombre del contenedor Docker',
    url VARCHAR(255) NULL COMMENT 'URL pública del servicio (puede ser NULL)',
    icon VARCHAR(10) NOT NULL DEFAULT '🔧' COMMENT 'Emoji/icono del servicio',
    description TEXT NOT NULL COMMENT 'Descripción del servicio',
    is_active BOOLEAN NOT NULL DEFAULT TRUE COMMENT 'Si el servicio está activo para monitoreo',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Fecha de creación',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Fecha de última actualización',
    
    INDEX idx_container_name (container_name),
    INDEX idx_is_active (is_active),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Servicios monitoreados por el sistema de health check';

-- Tabla para almacenar el historial de estados de los servicios (opcional para futuras mejoras)
CREATE TABLE IF NOT EXISTS service_status_history (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    service_id INT NOT NULL,
    status VARCHAR(50) NOT NULL COMMENT 'Estado del servicio (running, stopped, error, etc.)',
    state VARCHAR(50) NOT NULL COMMENT 'Estado detallado del contenedor',
    uptime VARCHAR(255) NULL COMMENT 'Tiempo de actividad',
    health VARCHAR(50) NULL COMMENT 'Estado de salud (healthy, unhealthy, no-healthcheck)',
    error_message TEXT NULL COMMENT 'Mensaje de error si aplica',
    checked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Momento de la verificación',
    
    FOREIGN KEY (service_id) REFERENCES monitored_services(id) ON DELETE CASCADE,
    INDEX idx_service_id (service_id),
    INDEX idx_checked_at (checked_at),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Historial de estados de los servicios monitoreados';

-- ============================================================================
-- INITIAL DATA
-- ============================================================================

-- Insertar los servicios actuales
INSERT INTO monitored_services (name, container_name, url, icon, description, is_active) VALUES
('Traefik', 'traefik', 'https://traefik.msarknet.me', '⚙️', 'Panel de control del proxy reverso', TRUE),
('Adminer', 'adminer', 'https://adminer.msarknet.me', '🗄️', 'Administrador de base de datos', TRUE),
('MariaDB', 'mariadb', NULL, '🗃️', 'Base de datos MariaDB', TRUE),
('Cerebro Frontend', 'cerebro-fe', 'https://msarknet.me', '🧠', 'Frontend React de Cerebro', TRUE),
('Cerebro Backend', 'cerebro-be', 'https://api.msarknet.me', '⚡', 'Backend Node.js de Cerebro', TRUE)
ON DUPLICATE KEY UPDATE
    name = VALUES(name),
    url = VALUES(url),
    icon = VALUES(icon),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- Verificar los datos insertados
SELECT 
    id,
    name,
    container_name,
    url,
    icon,
    description,
    is_active,
    created_at
FROM monitored_services 
ORDER BY id;
