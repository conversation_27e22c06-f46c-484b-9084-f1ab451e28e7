# Database Setup - Health Monitor

Este directorio contiene los archivos SQL necesarios para configurar la base de datos del sistema de monitoreo de servicios.

## Archivos

- **`schema.sql`** - Solo el schema de las tablas
- **`seeds.sql`** - Solo los datos iniciales (inserts)
- **`setup.sql`** - Schema completo + datos iniciales en un solo archivo

## Estructura de la Base de Datos

### Tabla: `monitored_services`

Almacena la configuración de los servicios que se van a monitorear.

| Campo | Tipo | Descripción |
|-------|------|-------------|
| `id` | INT AUTO_INCREMENT | ID único del servicio |
| `name` | VARCHAR(100) | Nombre del servicio |
| `container_name` | VARCHAR(100) | Nombre del contenedor Docker (único) |
| `url` | VARCHAR(255) | URL pública del servicio (puede ser NULL) |
| `icon` | VARCHAR(10) | Emoji/icono del servicio |
| `description` | TEXT | Descripción del servicio |
| `is_active` | BOOLEAN | Si el servicio está activo para monitoreo |
| `created_at` | TIMESTAMP | Fecha de creación |
| `updated_at` | TIMESTAMP | Fecha de última actualización |

### Tabla: `service_status_history` (opcional)

Para almacenar el historial de estados de los servicios.

| Campo | Tipo | Descripción |
|-------|------|-------------|
| `id` | BIGINT AUTO_INCREMENT | ID único del registro |
| `service_id` | INT | ID del servicio (FK) |
| `status` | VARCHAR(50) | Estado del servicio |
| `state` | VARCHAR(50) | Estado detallado del contenedor |
| `uptime` | VARCHAR(255) | Tiempo de actividad |
| `health` | VARCHAR(50) | Estado de salud |
| `error_message` | TEXT | Mensaje de error si aplica |
| `checked_at` | TIMESTAMP | Momento de la verificación |

## Cómo ejecutar

### Opción 1: Usando Adminer (Recomendado)

1. Accede a Adminer en `https://adminer.msarknet.me`
2. Conecta con las credenciales de MariaDB
3. Copia y pega el contenido de `setup.sql`
4. Ejecuta el script

### Opción 2: Usando línea de comandos

```bash
# Conectar al contenedor de MariaDB
docker exec -it mariadb mysql -u ${DB_USER} -p${DB_PASSWORD} ${DB_DATABASE}

# Ejecutar el script
source /path/to/setup.sql
```

### Opción 3: Desde fuera del contenedor

```bash
# Ejecutar el script directamente
docker exec -i mariadb mysql -u ${DB_USER} -p${DB_PASSWORD} ${DB_DATABASE} < health-be/database/setup.sql
```

## Variables de entorno necesarias

Asegúrate de que estas variables estén definidas en tu `.env`:

```env
DB_HOST=mariadb
DB_DATABASE=health_monitor
DB_USER=tu_usuario
DB_PASSWORD=tu_password
DB_ROOT_PASSWORD=tu_root_password
```

## Servicios incluidos

Los siguientes servicios se insertan automáticamente:

1. **Traefik** - Panel de control del proxy reverso
2. **Adminer** - Administrador de base de datos  
3. **MariaDB** - Base de datos MariaDB
4. **Cerebro Frontend** - Frontend React de Cerebro
5. **Cerebro Backend** - Backend Node.js de Cerebro

## Próximos pasos

Después de ejecutar este script, necesitarás:

1. Instalar un cliente MySQL/MariaDB en tu aplicación Node.js
2. Configurar la conexión a la base de datos
3. Modificar el código para leer los servicios desde la base de datos en lugar del array hardcodeado

### Dependencias recomendadas

```bash
npm install mysql2
# o
npm install mariadb
```
