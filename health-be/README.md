# Health Monitor Backend

API backend para monitorear el estado de contenedores Docker y servicios. Ahora con soporte para base de datos MariaDB para gestión dinámica de servicios.

## Características

- ✅ Monitoreo en tiempo real de contenedores Docker
- ✅ API REST para consultar estados de servicios
- ✅ Gestión dinámica de servicios mediante base de datos
- ✅ Soporte para CORS configurado para múltiples dominios
- ✅ Endpoints para crear, listar y consultar servicios
- ✅ Historial de estados (preparado para futuras implementaciones)

## Requisitos

- Node.js 18+
- Docker (acceso al socket)
- MariaDB 12.0.2+

## Instalación

1. **Instalar dependencias:**
   ```bash
   npm install
   ```

2. **Configurar variables de entorno:**
   ```bash
   cp .env.example .env
   # Editar .env con tus configuraciones
   ```

3. **Configurar la base de datos:**
   ```bash
   npm run migrate
   ```

## Variables de Entorno

```env
# Server
PORT=5001
NODE_ENV=development

# Database (MariaDB)
DB_HOST=mariadb
DB_PORT=3306
DB_DATABASE=health_monitor
DB_USER=health_user
DB_PASSWORD=your_secure_password
```

## Scripts Disponibles

```bash
# Desarrollo con hot reload
npm run dev

# Producción
npm start

# Configurar base de datos
npm run migrate
npm run db:setup  # alias de migrate

# Build (no-op para Node.js)
npm run build
```

## API Endpoints

### Health Check
- `GET /` - Estado general de la API
- `GET /api/health` - Health check con información de base de datos

### Servicios
- `GET /api/services` - Listar todos los servicios configurados
- `POST /api/services` - Crear un nuevo servicio
- `GET /api/services/status` - Estado de todos los servicios con información de contenedores
- `GET /api/services/:serviceName/status` - Estado de un servicio específico

### Docker
- `GET /api/docker/info` - Información general de Docker

## Estructura de Datos

### Servicio (Base de Datos)
```json
{
  "id": 1,
  "name": "Traefik",
  "containerName": "traefik",
  "url": "https://traefik.msarknet.me",
  "icon": "⚙️",
  "description": "Panel de control del proxy reverso",
  "isActive": true,
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### Estado de Servicio (API Response)
```json
{
  "id": 1,
  "name": "Traefik",
  "containerName": "traefik",
  "url": "https://traefik.msarknet.me",
  "icon": "⚙️",
  "description": "Panel de control del proxy reverso",
  "status": "running",
  "state": "running",
  "uptime": "Up 2 hours",
  "health": "healthy",
  "lastChecked": "2024-01-01T12:00:00.000Z"
}
```

## Estructura del Proyecto

```
health-be/
├── src/
│   ├── config/
│   │   └── database.js          # Configuración de MariaDB
│   ├── models/
│   │   └── MonitoredService.js  # Modelo para servicios
│   └── index.js                 # Servidor principal
├── database/
│   ├── schema.sql              # Solo schema
│   ├── seeds.sql               # Solo datos iniciales
│   ├── setup.sql               # Schema + datos completo
│   └── README.md               # Documentación de DB
├── scripts/
│   └── migrate.js              # Script de migración
├── .env.example                # Variables de entorno ejemplo
└── README.md                   # Este archivo
```

## Desarrollo

### Agregar un nuevo servicio via API

```bash
curl -X POST http://localhost:5001/api/services \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Mi Servicio",
    "containerName": "mi-contenedor",
    "url": "https://mi-servicio.com",
    "icon": "🚀",
    "description": "Descripción del servicio"
  }'
```

### Consultar estado de servicios

```bash
# Todos los servicios
curl http://localhost:5001/api/services/status

# Servicio específico
curl http://localhost:5001/api/services/traefik/status
```

## Docker

El servicio está configurado para ejecutarse en Docker con acceso al socket de Docker para monitorear otros contenedores.

```yaml
# docker-compose.yml
health-be:
  build: ./health-be
  volumes:
    - /var/run/docker.sock:/var/run/docker.sock:ro
  environment:
    - DB_HOST=mariadb
    - DB_DATABASE=health_monitor
    - DB_USER=${DB_USER}
    - DB_PASSWORD=${DB_PASSWORD}
  depends_on:
    - mariadb
```

## Próximas Mejoras

- [ ] Implementar historial de estados en `service_status_history`
- [ ] Agregar autenticación para endpoints de gestión
- [ ] Implementar webhooks para notificaciones
- [ ] Métricas y alertas avanzadas
- [ ] Dashboard web integrado
