{"name": "cerebro-be", "version": "1.0.0", "description": "Backend API for Docker container status monitoring", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build": "echo 'No build step needed for Node.js'", "migrate": "node scripts/migrate.js", "db:setup": "node scripts/migrate.js", "test:db": "node scripts/test-db.js"}, "dependencies": {"cors": "^2.8.5", "dockerode": "^4.0.2", "express": "^4.18.2", "mysql2": "^3.14.5"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["docker", "monitoring", "api"], "author": "MSarkNet", "license": "MIT"}