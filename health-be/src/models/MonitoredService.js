const { query, queryOne } = require('../config/database');

class MonitoredService {
  
  /**
   * Obtener todos los servicios activos
   * @returns {Promise<Array>} Lista de servicios monitoreados
   */
  static async getAll() {
    try {
      const sql = `
        SELECT 
          id,
          name,
          container_name,
          url,
          icon,
          description,
          is_active,
          created_at,
          updated_at
        FROM monitored_services 
        WHERE is_active = TRUE
        ORDER BY name ASC
      `;
      
      return await query(sql);
    } catch (error) {
      console.error('Error getting all monitored services:', error.message);
      throw error;
    }
  }

  /**
   * Obtener un servicio por su nombre de contenedor
   * @param {string} containerName - Nombre del contenedor
   * @returns {Promise<Object|null>} Servicio encontrado o null
   */
  static async getByContainerName(containerName) {
    try {
      const sql = `
        SELECT 
          id,
          name,
          container_name,
          url,
          icon,
          description,
          is_active,
          created_at,
          updated_at
        FROM monitored_services 
        WHERE container_name = ? AND is_active = TRUE
      `;
      
      return await queryOne(sql, [containerName]);
    } catch (error) {
      console.error(`Error getting service by container name ${containerName}:`, error.message);
      throw error;
    }
  }

  /**
   * Obtener un servicio por su nombre o nombre de contenedor
   * @param {string} serviceName - Nombre del servicio o contenedor
   * @returns {Promise<Object|null>} Servicio encontrado o null
   */
  static async getByName(serviceName) {
    try {
      const sql = `
        SELECT 
          id,
          name,
          container_name,
          url,
          icon,
          description,
          is_active,
          created_at,
          updated_at
        FROM monitored_services 
        WHERE (LOWER(name) = LOWER(?) OR container_name = ?) 
          AND is_active = TRUE
      `;
      
      return await queryOne(sql, [serviceName, serviceName]);
    } catch (error) {
      console.error(`Error getting service by name ${serviceName}:`, error.message);
      throw error;
    }
  }

  /**
   * Crear un nuevo servicio monitoreado
   * @param {Object} serviceData - Datos del servicio
   * @returns {Promise<number>} ID del servicio creado
   */
  static async create(serviceData) {
    try {
      const { name, containerName, url, icon, description, isActive = true } = serviceData;
      
      const sql = `
        INSERT INTO monitored_services 
        (name, container_name, url, icon, description, is_active)
        VALUES (?, ?, ?, ?, ?, ?)
      `;
      
      const result = await query(sql, [name, containerName, url, icon, description, isActive]);
      return result.insertId;
    } catch (error) {
      console.error('Error creating monitored service:', error.message);
      throw error;
    }
  }

  /**
   * Actualizar un servicio existente
   * @param {number} id - ID del servicio
   * @param {Object} serviceData - Datos a actualizar
   * @returns {Promise<boolean>} True si se actualizó correctamente
   */
  static async update(id, serviceData) {
    try {
      const { name, containerName, url, icon, description, isActive } = serviceData;
      
      const sql = `
        UPDATE monitored_services 
        SET name = ?, container_name = ?, url = ?, icon = ?, 
            description = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      const result = await query(sql, [name, containerName, url, icon, description, isActive, id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`Error updating service ${id}:`, error.message);
      throw error;
    }
  }

  /**
   * Eliminar (desactivar) un servicio
   * @param {number} id - ID del servicio
   * @returns {Promise<boolean>} True si se desactivó correctamente
   */
  static async delete(id) {
    try {
      const sql = `
        UPDATE monitored_services 
        SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `;
      
      const result = await query(sql, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error(`Error deleting service ${id}:`, error.message);
      throw error;
    }
  }

  /**
   * Contar servicios activos
   * @returns {Promise<number>} Número de servicios activos
   */
  static async count() {
    try {
      const sql = 'SELECT COUNT(*) as count FROM monitored_services WHERE is_active = TRUE';
      const result = await queryOne(sql);
      return result.count;
    } catch (error) {
      console.error('Error counting services:', error.message);
      throw error;
    }
  }
}

module.exports = MonitoredService;
